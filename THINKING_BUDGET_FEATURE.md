# Thinking Budget Automation Feature

## Overview

The Thinking Budget feature automatically controls the "Set Thinking Budget to Max" functionality in Google AI Studio. This feature integrates seamlessly with the existing system prompt automation and other extension features.

## How It Works

### Target Elements
- **Primary Target**: But<PERSON> with `aria-label="Toggle thinking budget between auto and manual"`
- **Secondary Target**: Temperature slider with `aria-label="Temperature"` and `type="range"`

### Functionality
1. **Detection**: Uses MutationObserver to detect when the thinking budget button becomes available
2. **State Management**: Reads the button's `aria-checked` attribute to determine current state
   - `"true"` = Manual mode (max thinking budget)
   - `"false"` = Auto mode
3. **Automation**: When enabled, automatically:
   - Sets thinking budget to manual mode
   - Locates the temperature slider
   - Sets temperature to maximum value
   - Dispatches proper events for UI synchronization

### User Interface

#### Toggle Control
- Located in the extension popup after URL Context toggle
- Label: "Set Thinking Budget to Max"
- Description: "Automatically sets thinking budget to manual mode with maximum temperature"
- Default state: Disabled (unchecked)

#### Status Indicator
- Shows current page state: "Manual mode on page" / "Auto mode on page"
- Displays "Button not found" if feature is not available
- Shows "N/A (not AI Studio)" when not on the correct domain

## Technical Implementation

### Content Script (content.js)
```javascript
// Key functions added:
- findThinkingBudgetButton()
- findTemperatureSlider()
- isThinkingBudgetManual()
- setThinkingBudgetToManual()
- setTemperatureToMax()
- applyThinkingBudgetSettings()
- retryThinkingBudgetSettings()
```

### Popup Interface (popup.js)
```javascript
// Key additions:
- thinkingBudgetToggle DOM element
- thinkingBudgetStatusText DOM element
- updateThinkingBudgetStatusDisplay()
- Integration with saveSettings() and loadSettings()
```

### Storage
- Setting key: `thinkingBudgetEnabled` (boolean)
- Default value: `false`
- Synced across browser instances via `chrome.storage.sync`

## Timing and Reliability

### Retry Mechanism
- **Initial delay**: 500ms after page load
- **Retry interval**: 2 seconds
- **Maximum retries**: 15 attempts (30 seconds total)
- **Fallback**: Logs failure if button not found after all retries

### Event Handling
- Proper event dispatching for input and change events
- UI state synchronization with `aria-valuetext` updates
- Error handling for missing elements or failed operations

## User Experience

### Notifications
- Success: "Thinking budget set to max with maximum temperature"
- Partial success: "Thinking budget set to manual (temperature adjustment failed)"
- Error: "Error setting thinking budget"

### Integration
- Works independently of other extension features
- Applies automatically when extension is enabled and feature is toggled on
- Respects global extension enable/disable state

## Error Handling

### Common Scenarios
1. **Button not found**: Logs warning, continues retry mechanism
2. **Temperature slider missing**: Sets thinking budget but skips temperature adjustment
3. **Click failure**: Logs error, shows user notification
4. **Permission issues**: Graceful degradation with error messages

### Debugging
- Comprehensive console logging for troubleshooting
- Clear error messages in popup status indicators
- Detailed logging of retry attempts and failures

## Configuration

### Default Settings
```javascript
{
  thinkingBudgetEnabled: false  // Disabled by default for safety
}
```

### User Control
- Toggle in extension popup
- Real-time status updates
- Immediate application when enabled
- Persistent settings across browser sessions

## Testing Scenarios

### Recommended Tests
1. **Basic functionality**: Enable toggle, verify thinking budget is set to manual
2. **Temperature setting**: Confirm temperature slider is set to maximum
3. **Page navigation**: Test persistence across different AI Studio pages
4. **Error handling**: Test on pages without thinking budget controls
5. **Retry mechanism**: Test on slowly loading pages
6. **Integration**: Verify compatibility with other extension features

### Edge Cases
- Pages that load thinking budget controls dynamically
- Different AI Studio page types (new prompts, existing prompts, different models)
- Network delays affecting element availability
- Multiple rapid setting changes

## Future Enhancements

### Potential Improvements
1. **Custom temperature values**: Allow users to set specific temperature values
2. **Model-specific settings**: Different settings for different AI models
3. **Advanced timing controls**: User-configurable retry intervals
4. **Batch operations**: Apply settings to multiple prompts simultaneously

### Compatibility
- Designed to be forward-compatible with AI Studio UI changes
- Robust element detection using multiple selector strategies
- Graceful degradation when features are unavailable

## Security Considerations

### Safe Defaults
- Feature disabled by default to prevent unexpected behavior
- Only activates on verified aistudio.google.com domain
- No external network requests or data transmission

### User Control
- Clear user consent required via toggle
- Transparent status reporting
- Easy disable mechanism

## Support and Troubleshooting

### Common Issues
1. **"Button not found"**: Feature may not be available on current page type
2. **Slow activation**: Allow up to 30 seconds for detection on slow connections
3. **Settings not persisting**: Check Chrome storage permissions

### Debug Information
- Check browser console for detailed logs
- Verify extension permissions in Chrome settings
- Confirm you're on a supported AI Studio page type
