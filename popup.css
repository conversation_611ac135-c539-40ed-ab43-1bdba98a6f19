/* Popup styles for AI Studio System Prompt Automation */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    width: 350px;
    min-height: 500px;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 600px;
}

/* Header */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    text-align: center;
}

header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.subtitle {
    font-size: 12px;
    opacity: 0.9;
}

/* Main Content */
main {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
}

.setting-group {
    margin-bottom: 12px;
}

.label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #555;
}

/* Toggle Switch */
.toggle-container {
    display: flex;
    align-items: center;
    padding: 12px 0;
}

.toggle-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    transition: background 0.3s ease;
    margin-right: 12px;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-label input:checked + .toggle-slider {
    background: #667eea;
}

.toggle-label input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-text {
    font-weight: 500;
    color: #333;
}

.setting-status {
    font-size: 11px;
    color: #555;
    margin-top: 6px;
    padding-left: 5px; /* Align slightly with toggle text if toggle has margin */
}

.setting-description {
    font-size: 11px;
    color: #888;
    margin-top: 4px;
    padding-left: 5px; /* Align with toggle text */
    line-height: 1.3;
    font-style: italic;
}

/* Textarea */
textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    resize: vertical;
    min-height: 90px;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-counter {
    text-align: right;
    font-size: 11px;
    color: #666;
    margin-top: 4px;
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 12px; /* Also reducing margin for consistency */
}

.btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #28a745;
    color: white;
}

.btn-secondary:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-tertiary {
    background: #6c757d;
    color: white;
}

.btn-tertiary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Status Messages */
.status-message {
    padding: 10px 12px;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.hidden {
    display: none !important;
}

/* Domain Status */
.info-section {
    border-top: 1px solid #e1e5e9;
    padding-top: 16px;
}

.domain-status {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.active {
    background: #28a745;
}

.status-indicator.inactive {
    background: #dc3545;
}

/* Footer */
footer {
    border-top: 1px solid #e1e5e9;
    padding: 12px 20px;
    background: white;
}

.footer-links {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: #666;
}

.footer-links a {
    color: #667eea;
    text-decoration: none;
}

.footer-links a:hover {
    text-decoration: underline;
}

.separator {
    margin: 0 8px;
}

.version {
    color: #999;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px 20px 20px;
    border-bottom: 1px solid #e1e5e9;
    position: relative;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1001;
}

.close-btn:hover {
    color: #333;
    background: #f5f5f5;
    border-radius: 50%;
}

.modal-body {
    padding: 20px;
}

.modal-body h4 {
    margin: 16px 0 8px 0;
    color: #333;
}

.modal-body ol, .modal-body ul {
    margin-left: 20px;
    margin-bottom: 16px;
}

.modal-body li {
    margin-bottom: 4px;
    line-height: 1.5;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
